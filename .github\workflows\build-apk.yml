name: Build Android APK

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y openjdk-8-jdk build-essential libssl-dev libffi-dev python3-dev
        sudo apt-get install -y libtool pkg-config zlib1g-dev libncurses5-dev libncursesw5-dev libtinfo5 cmake libffi-dev libssl-dev
        
    - name: Set JAVA_HOME
      run: echo "JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64" >> $GITHUB_ENV

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install buildozer cython

    - name: Build APK with Buildozer
      run: |
        buildozer android debug

    - name: Upload APK artifact
      uses: actions/upload-artifact@v3
      with:
        name: pdf-app-restrictor-apk
        path: bin/*.apk

    - name: List files for debugging
      if: always()
      run: |
        echo "Contents of bin directory:"
        ls -la bin/ || echo "bin directory not found"
        echo "Contents of .buildozer directory:"
        ls -la .buildozer/ || echo ".buildozer directory not found"
