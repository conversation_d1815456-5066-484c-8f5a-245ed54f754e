
# Alternative Build Methods for PDF App Restrictor

## Method 1: Using Linux/WSL (Recommended)

1. Install WSL on Windows or use a Linux machine
2. Install Python 3.8+
3. Install dependencies:
   ```bash
   sudo apt update
   sudo apt install python3-pip python3-venv git
   sudo apt install openjdk-8-jdk
   sudo apt install build-essential libssl-dev libffi-dev python3-dev
   ```

4. Install buildozer:
   ```bash
   pip3 install buildozer
   ```

5. Build APK:
   ```bash
   buildozer android debug
   ```

## Method 2: Using Docker

Create a Dockerfile:
```dockerfile
FROM ubuntu:20.04

RUN apt-get update && apt-get install -y \
    python3 python3-pip git openjdk-8-jdk build-essential \
    libssl-dev libffi-dev python3-dev

RUN pip3 install buildozer

WORKDIR /app
COPY . .

RUN buildozer android debug
```

Build with:
```bash
docker build -t pdf-app-restrictor .
docker run -v $(pwd)/bin:/app/bin pdf-app-restrictor
```

## Method 3: GitHub Actions

Create `.github/workflows/build.yml`:
```yaml
name: Build APK

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install openjdk-8-jdk
        pip install buildozer
    
    - name: Build APK
      run: buildozer android debug
    
    - name: Upload APK
      uses: actions/upload-artifact@v2
      with:
        name: apk
        path: bin/*.apk
```

## Method 4: Online Build Services

1. **Replit**: Upload your code to Replit and use their Linux environment
2. **Gitpod**: Use Gitpod's cloud development environment
3. **CodeSandbox**: For testing and development

## Manual Installation on Android

If you can't build the APK, you can still run the app:

1. Install Termux on Android
2. Install Python in Termux:
   ```bash
   pkg install python
   ```
3. Install dependencies:
   ```bash
   pip install kivymd PyMuPDF
   ```
4. Copy your app files to Termux
5. Run: `python main.py`

Note: Some Android-specific features may not work in Termux.
