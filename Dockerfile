FROM ubuntu:20.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    openjdk-8-jdk \
    build-essential \
    libssl-dev \
    libffi-dev \
    libtool \
    pkg-config \
    zlib1g-dev \
    libncurses5-dev \
    libncursesw5-dev \
    libtinfo5 \
    cmake \
    && rm -rf /var/lib/apt/lists/*

# Set JAVA_HOME
ENV JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

# Install Python dependencies
RUN pip3 install buildozer cython

# Set working directory
WORKDIR /app

# Copy project files
COPY . .

# Build APK
RUN buildozer android debug

# Command to run when container starts
CMD ["ls", "-la", "bin/"]
