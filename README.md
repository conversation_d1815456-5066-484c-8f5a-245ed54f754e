# PDF App Restrictor

A KivyMD Android application that restricts access to selected apps until the user reads PDF pages for a specified time (5 minutes).

## Features

- **App Selection**: Choose which apps you want to restrict
- **PDF Reading**: Read pages from a PDF with a built-in timer
- **Time-based Access**: Gain 1 hour of access to restricted apps after completing a 5-minute reading session
- **Background Monitoring**: Continuously monitors app launches and enforces restrictions
- **Persistent Settings**: Saves your app selections and access status

## How It Works

1. **Setup**: Select the apps you want to restrict from the installed apps list
2. **Reading Session**: When you want to access a restricted app, start a reading session
3. **Timer**: Read the PDF for 5 minutes while the timer counts down
4. **Access Granted**: After completing the reading session, you get 1 hour of unrestricted access
5. **Automatic Blocking**: If you try to open a restricted app without current access, you'll be redirected to the reading screen

## Installation

### Prerequisites

- Python 3.8 or higher
- Android SDK and NDK (for building APK)
- Buildozer

### Setup Steps

1. **Clone or download this project**
2. **Install Python dependencies**:
   ```bash
   python install_dependencies.py
   ```
   
3. **Install Buildozer**:
   ```bash
   pip install buildozer
   ```

4. **Install Android SDK and NDK**:
   - Download Android Studio or standalone SDK
   - Set environment variables for ANDROID_HOME and ANDROID_NDK_HOME

5. **Build the APK**:
   ```bash
   buildozer android debug
   ```

6. **Install on Android device**:
   ```bash
   adb install bin/pdfrestrictor-0.1-debug.apk
   ```

## Required Android Permissions

The app requires several permissions to function properly:

- **Usage Stats**: To monitor which apps are being launched
- **Display Over Other Apps**: To block restricted apps and show the reading screen
- **Storage**: To read the PDF file
- **Internet**: For potential future features

## File Structure

```
pdf-app-restrictor/
├── main.py                    # Main application file
├── app_restrictor_service.py  # Background service for app monitoring
├── requirements.txt           # Python dependencies
├── buildozer.spec            # Android build configuration
├── install_dependencies.py   # Dependency installation script
├── sample.pdf               # Sample PDF file for reading
└── README.md                # This file
```

## Usage

### First Time Setup

1. **Launch the app** on your Android device
2. **Grant permissions** when prompted:
   - Usage Stats access
   - Display over other apps
3. **Select apps** to restrict using the "Select Apps to Restrict" button
4. **Save your selection**

### Daily Usage

1. **Try to open a restricted app** - you'll be redirected to the PDF reader
2. **Start a reading session** by tapping "Start Reading Timer"
3. **Read the PDF** for 5 minutes while the timer counts down
4. **Complete the session** to gain 1 hour of access to restricted apps

### Managing Restrictions

- **Add/Remove Apps**: Use the app selection screen to modify which apps are restricted
- **Check Status**: The main screen shows your current access status and remaining time
- **Reading Progress**: Navigate through PDF pages using the arrow buttons

## Technical Details

### App Monitoring

The app uses Android's ActivityManager and UsageStats APIs to monitor app launches in the background. When a restricted app is detected, it immediately redirects the user to the PDF reading screen.

### PDF Handling

PDF files are processed using PyMuPDF (fitz) library, which converts PDF pages to images for display in the Kivy interface.

### Data Persistence

App settings and access status are stored in JSON format locally on the device.

## Troubleshooting

### Common Issues

1. **App not blocking restricted apps**:
   - Ensure Usage Stats permission is granted
   - Check that the app is running in the background

2. **PDF not displaying**:
   - Verify that sample.pdf exists in the app directory
   - Check that PyMuPDF is properly installed

3. **Build errors**:
   - Ensure Android SDK and NDK are properly installed
   - Check that all Python dependencies are installed
   - Verify buildozer.spec configuration

### Permissions Not Working

If permissions are not being granted:

1. Go to Android Settings > Apps > PDF App Restrictor
2. Manually grant all required permissions
3. For Usage Stats: Settings > Security > Apps with usage access
4. For Display Over Apps: Settings > Apps > Special access > Display over other apps

## Development

### Testing on Desktop

The app can be run on desktop for development purposes:

```bash
python main.py
```

Note: App monitoring features will not work on desktop, but the UI and PDF reading functionality can be tested.

### Building for Release

To build a release APK:

```bash
buildozer android release
```

Remember to sign the APK for distribution.

## License

This project is provided as-is for educational and personal use.

## Contributing

Feel free to submit issues and enhancement requests!
#   M o b i l e - a p p 
 
 