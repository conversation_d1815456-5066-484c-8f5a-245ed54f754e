"""
App Restrictor Service
Handles the background monitoring and restriction of selected apps
"""

import json
import os
import time
import threading
from datetime import datetime, timedelta
from kivy.utils import platform

if platform == 'android':
    from jnius import autoclass, cast
    from android.runnable import run_on_ui_thread
    
    PythonActivity = autoclass('org.kivy.android.PythonActivity')
    Intent = autoclass('android.content.Intent')
    ActivityManager = autoclass('android.app.ActivityManager')
    Context = autoclass('android.content.Context')
    Settings = autoclass('android.provider.Settings')
    Uri = autoclass('android.net.Uri')


class AppRestrictorService:
    def __init__(self):
        self.restricted_apps = []
        self.is_monitoring = False
        self.last_reading_completion = None
        self.access_granted_until = None
        self.data_file = "app_data.json"
        self.monitoring_thread = None
        
        # Load saved data
        self.load_data()

    def load_data(self):
        """Load saved app restrictions and access data"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                    self.restricted_apps = data.get('restricted_apps', [])
                    
                    # Load last reading completion time
                    last_completion_str = data.get('last_reading_completion')
                    if last_completion_str:
                        self.last_reading_completion = datetime.fromisoformat(last_completion_str)
                        
                    # Check if access is still valid (1 hour after reading completion)
                    if self.last_reading_completion:
                        self.access_granted_until = self.last_reading_completion + timedelta(hours=1)
                        
        except Exception as e:
            print(f"Error loading service data: {e}")

    def save_data(self):
        """Save app restrictions and access data"""
        try:
            data = {
                'restricted_apps': self.restricted_apps,
                'last_reading_completion': self.last_reading_completion.isoformat() if self.last_reading_completion else None,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.data_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            print(f"Error saving service data: {e}")

    def update_restricted_apps(self, apps_list):
        """Update the list of restricted apps"""
        self.restricted_apps = apps_list
        self.save_data()

    def grant_access_after_reading(self):
        """Grant access to restricted apps after successful reading session"""
        self.last_reading_completion = datetime.now()
        self.access_granted_until = self.last_reading_completion + timedelta(hours=1)
        self.save_data()
        print(f"Access granted until: {self.access_granted_until}")

    def is_access_granted(self):
        """Check if user currently has access to restricted apps"""
        if not self.access_granted_until:
            return False
        
        return datetime.now() < self.access_granted_until

    def start_monitoring(self):
        """Start monitoring for restricted app launches"""
        if not self.is_monitoring and platform == 'android':
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitor_apps, daemon=True)
            self.monitoring_thread.start()
            print("App monitoring started")

    def stop_monitoring(self):
        """Stop monitoring for restricted app launches"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1)
        print("App monitoring stopped")

    def _monitor_apps(self):
        """Background thread to monitor app launches"""
        if platform != 'android':
            return
            
        try:
            context = PythonActivity.mActivity
            activity_manager = context.getSystemService(Context.ACTIVITY_SERVICE)
            
            while self.is_monitoring:
                try:
                    # Get running tasks
                    running_tasks = activity_manager.getRunningTasks(1)
                    
                    if running_tasks and len(running_tasks) > 0:
                        top_activity = running_tasks[0].topActivity
                        current_package = str(top_activity.getPackageName())
                        
                        # Check if current app is restricted
                        if current_package in self.restricted_apps:
                            if not self.is_access_granted():
                                # Block the app and redirect to our app
                                self._block_app_and_redirect()
                    
                    time.sleep(2)  # Check every 2 seconds
                    
                except Exception as e:
                    print(f"Error in app monitoring: {e}")
                    time.sleep(5)  # Wait longer on error
                    
        except Exception as e:
            print(f"Error starting app monitoring: {e}")

    def _block_app_and_redirect(self):
        """Block restricted app and redirect to our PDF reading app"""
        try:
            # Create intent to launch our app
            intent = Intent()
            intent.setClassName("org.example.pdfrestrictor", "org.kivy.android.PythonActivity")
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
            
            # Add extra data to indicate app was blocked
            intent.putExtra("blocked_app", True)
            
            context = PythonActivity.mActivity
            context.startActivity(intent)
            
            print("Redirected to PDF reading app")
            
        except Exception as e:
            print(f"Error redirecting to app: {e}")

    def check_usage_stats_permission(self):
        """Check if app has usage stats permission (required for app monitoring)"""
        if platform != 'android':
            return True
            
        try:
            context = PythonActivity.mActivity
            app_ops = context.getSystemService(Context.APP_OPS_SERVICE)
            mode = app_ops.checkOpNoThrow("android:get_usage_stats", 
                                         context.getApplicationInfo().uid, 
                                         context.getPackageName())
            return mode == 0  # MODE_ALLOWED
        except Exception as e:
            print(f"Error checking usage stats permission: {e}")
            return False

    def request_usage_stats_permission(self):
        """Request usage stats permission from user"""
        if platform != 'android':
            return
            
        try:
            intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
            context = PythonActivity.mActivity
            context.startActivity(intent)
        except Exception as e:
            print(f"Error requesting usage stats permission: {e}")

    def check_overlay_permission(self):
        """Check if app has overlay permission (required for blocking apps)"""
        if platform != 'android':
            return True
            
        try:
            if hasattr(Settings, 'canDrawOverlays'):
                context = PythonActivity.mActivity
                return Settings.canDrawOverlays(context)
            return True
        except Exception as e:
            print(f"Error checking overlay permission: {e}")
            return False

    def request_overlay_permission(self):
        """Request overlay permission from user"""
        if platform != 'android':
            return
            
        try:
            intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
            context = PythonActivity.mActivity
            package_uri = Uri.parse(f"package:{context.getPackageName()}")
            intent.setData(package_uri)
            context.startActivity(intent)
        except Exception as e:
            print(f"Error requesting overlay permission: {e}")

    def get_time_until_access_expires(self):
        """Get remaining time until access expires"""
        if not self.access_granted_until:
            return None
            
        remaining = self.access_granted_until - datetime.now()
        if remaining.total_seconds() <= 0:
            return None
            
        return remaining

    def get_status_info(self):
        """Get current status information"""
        status = {
            'restricted_apps_count': len(self.restricted_apps),
            'access_granted': self.is_access_granted(),
            'monitoring_active': self.is_monitoring,
            'last_reading': self.last_reading_completion.isoformat() if self.last_reading_completion else None,
            'access_expires': self.access_granted_until.isoformat() if self.access_granted_until else None
        }
        
        time_remaining = self.get_time_until_access_expires()
        if time_remaining:
            status['time_remaining'] = str(time_remaining).split('.')[0]  # Remove microseconds
            
        return status


# Global service instance
app_restrictor_service = AppRestrictorService()
