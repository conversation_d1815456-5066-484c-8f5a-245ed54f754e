#!/usr/bin/env python3
"""
APK Build Script for PDF App Restrictor
Handles building the Android APK with proper error handling and platform detection
"""

import os
import sys
import subprocess
import platform
import shutil

def check_requirements():
    """Check if all build requirements are met"""
    print("Checking build requirements...")
    
    # Check if buildozer is installed
    try:
        subprocess.run(['buildozer', '--version'], capture_output=True, check=True)
        print("✓ Buildozer is installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ Buildozer is not installed or not working")
        print("Install with: pip install buildozer")
        return False
    
    # Check if buildozer.spec exists
    if not os.path.exists('buildozer.spec'):
        print("✗ buildozer.spec not found")
        return False
    else:
        print("✓ buildozer.spec found")
    
    # Check if main.py exists
    if not os.path.exists('main.py'):
        print("✗ main.py not found")
        return False
    else:
        print("✓ main.py found")
    
    # Check if sample.pdf exists
    if not os.path.exists('sample.pdf'):
        print("✗ sample.pdf not found")
        return False
    else:
        print("✓ sample.pdf found")
    
    return True

def setup_android_environment():
    """Setup Android build environment"""
    print("\nSetting up Android environment...")
    
    # Check platform
    current_platform = platform.system()
    print(f"Platform: {current_platform}")
    
    if current_platform == "Windows":
        print("Note: Building Android APKs on Windows can be challenging.")
        print("Consider using WSL (Windows Subsystem for Linux) or a Linux VM.")
        print("Alternatively, use GitHub Actions or other CI/CD services.")
        return False
    
    # Check for Java
    try:
        result = subprocess.run(['java', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Java is installed")
        else:
            print("✗ Java not found")
            return False
    except FileNotFoundError:
        print("✗ Java not found")
        print("Install Java 8 or 11 for Android development")
        return False
    
    return True

def build_apk():
    """Build the Android APK"""
    print("\nBuilding Android APK...")
    
    try:
        # Clean previous builds
        if os.path.exists('.buildozer'):
            print("Cleaning previous build...")
            shutil.rmtree('.buildozer')
        
        # Run buildozer
        print("Running buildozer android debug...")
        result = subprocess.run(['buildozer', 'android', 'debug'], 
                              capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✓ APK built successfully!")
            
            # Check if APK was created
            bin_dir = 'bin'
            if os.path.exists(bin_dir):
                apk_files = [f for f in os.listdir(bin_dir) if f.endswith('.apk')]
                if apk_files:
                    apk_path = os.path.join(bin_dir, apk_files[0])
                    print(f"✓ APK location: {apk_path}")
                    return True
            
            print("✗ APK file not found in bin directory")
            return False
        else:
            print("✗ APK build failed")
            return False
            
    except Exception as e:
        print(f"✗ Build error: {e}")
        return False

def create_alternative_instructions():
    """Create instructions for alternative build methods"""
    instructions = """
# Alternative Build Methods for PDF App Restrictor

## Method 1: Using Linux/WSL (Recommended)

1. Install WSL on Windows or use a Linux machine
2. Install Python 3.8+
3. Install dependencies:
   ```bash
   sudo apt update
   sudo apt install python3-pip python3-venv git
   sudo apt install openjdk-8-jdk
   sudo apt install build-essential libssl-dev libffi-dev python3-dev
   ```

4. Install buildozer:
   ```bash
   pip3 install buildozer
   ```

5. Build APK:
   ```bash
   buildozer android debug
   ```

## Method 2: Using Docker

Create a Dockerfile:
```dockerfile
FROM ubuntu:20.04

RUN apt-get update && apt-get install -y \\
    python3 python3-pip git openjdk-8-jdk build-essential \\
    libssl-dev libffi-dev python3-dev

RUN pip3 install buildozer

WORKDIR /app
COPY . .

RUN buildozer android debug
```

Build with:
```bash
docker build -t pdf-app-restrictor .
docker run -v $(pwd)/bin:/app/bin pdf-app-restrictor
```

## Method 3: GitHub Actions

Create `.github/workflows/build.yml`:
```yaml
name: Build APK

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install openjdk-8-jdk
        pip install buildozer
    
    - name: Build APK
      run: buildozer android debug
    
    - name: Upload APK
      uses: actions/upload-artifact@v2
      with:
        name: apk
        path: bin/*.apk
```

## Method 4: Online Build Services

1. **Replit**: Upload your code to Replit and use their Linux environment
2. **Gitpod**: Use Gitpod's cloud development environment
3. **CodeSandbox**: For testing and development

## Manual Installation on Android

If you can't build the APK, you can still run the app:

1. Install Termux on Android
2. Install Python in Termux:
   ```bash
   pkg install python
   ```
3. Install dependencies:
   ```bash
   pip install kivymd PyMuPDF
   ```
4. Copy your app files to Termux
5. Run: `python main.py`

Note: Some Android-specific features may not work in Termux.
"""
    
    with open('BUILD_INSTRUCTIONS.md', 'w') as f:
        f.write(instructions)
    
    print("✓ Created BUILD_INSTRUCTIONS.md with alternative build methods")

def main():
    """Main build function"""
    print("PDF App Restrictor - APK Builder")
    print("=" * 40)
    
    if not check_requirements():
        print("\n✗ Build requirements not met")
        create_alternative_instructions()
        return False
    
    if not setup_android_environment():
        print("\n✗ Android environment setup failed")
        create_alternative_instructions()
        return False
    
    if build_apk():
        print("\n✓ Build completed successfully!")
        return True
    else:
        print("\n✗ Build failed")
        create_alternative_instructions()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
