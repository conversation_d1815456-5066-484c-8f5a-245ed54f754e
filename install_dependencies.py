#!/usr/bin/env python3
"""
Installation script for PDF App Restrictor dependencies
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package}: {e}")
        return False

def main():
    """Install all required dependencies"""
    print("Installing dependencies for PDF App Restrictor...")
    print("=" * 50)
    
    # Read requirements from file
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"Error: {requirements_file} not found!")
        return False
    
    with open(requirements_file, 'r') as f:
        packages = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("=" * 50)
    print(f"Installation complete: {success_count}/{total_count} packages installed successfully")
    
    if success_count == total_count:
        print("✓ All dependencies installed successfully!")
        print("\nNext steps:")
        print("1. Install Buildozer: pip install buildozer")
        print("2. Install Android SDK and NDK")
        print("3. Run: buildozer android debug")
        return True
    else:
        print("✗ Some dependencies failed to install. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
