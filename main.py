"""
PDF Reading App Restrictor
A KivyMD Android app that restricts access to selected apps until user reads PDF pages for 5 minutes
"""

from kivymd.app import <PERSON><PERSON>pp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.list import MDList, OneLineListItem, OneLineAvatarIconListItem
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.card import MDCard
from kivymd.uix.progressbar import MDProgressBar
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField

from kivy.clock import Clock
from kivy.metrics import dp
from kivy.core.window import Window
from kivy.utils import platform

import json
import os
import threading
import time
from datetime import datetime, timedelta

# Import our app restrictor service
from app_restrictor_service import app_restrictor_service

# Import for PDF handling
try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None

# Import for Android app management
if platform == 'android':
    from jnius import autoclass, cast
    from android.permissions import request_permissions, Permission
    from android.runnable import run_on_ui_thread
    
    PythonActivity = autoclass('org.kivy.android.PythonActivity')
    Intent = autoclass('android.content.Intent')
    PackageManager = autoclass('android.content.pm.PackageManager')
    ApplicationInfo = autoclass('android.content.pm.ApplicationInfo')
    Context = autoclass('android.content.Context')


class PDFReadingAppRestrictor(MDApp):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "PDF App Restrictor"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"

        # App data
        self.pdf_pages = []
        self.current_page = 0
        self.reading_timer = 0
        self.timer_active = False

        # Use the global service for app restriction
        self.service = app_restrictor_service

        # Load PDF
        self.load_pdf()

    def build(self):
        # Request permissions on Android
        if platform == 'android':
            self.request_android_permissions()
        
        # Create screen manager
        self.screen_manager = MDScreenManager()
        
        # Create screens
        self.create_main_screen()
        self.create_app_selection_screen()
        self.create_pdf_reading_screen()
        
        return self.screen_manager

    def request_android_permissions(self):
        """Request necessary Android permissions"""
        permissions = [
            Permission.READ_EXTERNAL_STORAGE,
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.QUERY_ALL_PACKAGES,
            Permission.PACKAGE_USAGE_STATS
        ]
        request_permissions(permissions)

    def on_start(self):
        """Called when the app starts"""
        # Start the app monitoring service
        self.service.start_monitoring()

        # Check for required permissions
        self.check_permissions()

    def on_stop(self):
        """Called when the app stops"""
        # Stop the monitoring service
        self.service.stop_monitoring()

    def check_permissions(self):
        """Check and request necessary permissions"""
        if platform == 'android':
            # Check usage stats permission
            if not self.service.check_usage_stats_permission():
                self.show_permission_dialog("Usage Stats",
                    "This app needs usage stats permission to monitor app launches.",
                    self.service.request_usage_stats_permission)

            # Check overlay permission
            if not self.service.check_overlay_permission():
                self.show_permission_dialog("Display Over Other Apps",
                    "This app needs permission to display over other apps to block restricted apps.",
                    self.service.request_overlay_permission)

    def show_permission_dialog(self, permission_name, message, callback):
        """Show permission request dialog"""
        dialog = MDDialog(
            title=f"{permission_name} Permission Required",
            text=message,
            buttons=[
                MDRaisedButton(
                    text="Grant Permission",
                    on_release=lambda x: (dialog.dismiss(), callback())
                ),
                MDRaisedButton(
                    text="Cancel",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def load_pdf(self):
        """Load PDF pages from sample.pdf"""
        try:
            if fitz:
                pdf_path = "sample.pdf"
                if os.path.exists(pdf_path):
                    doc = fitz.open(pdf_path)
                    self.pdf_pages = []
                    for page_num in range(len(doc)):
                        page = doc.load_page(page_num)
                        # Convert page to image for display
                        mat = fitz.Matrix(2, 2)  # Zoom factor
                        pix = page.get_pixmap(matrix=mat)
                        img_data = pix.tobytes("png")
                        self.pdf_pages.append(img_data)
                    doc.close()
                    print(f"Loaded {len(self.pdf_pages)} PDF pages")
                else:
                    print("sample.pdf not found")
            else:
                print("PyMuPDF not available")
        except Exception as e:
            print(f"Error loading PDF: {e}")

    def create_main_screen(self):
        """Create the main screen with navigation options"""
        screen = MDScreen(name="main")
        
        layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(20),
            adaptive_height=True,
            pos_hint={"center_x": 0.5, "center_y": 0.5}
        )
        
        # Title
        title = MDLabel(
            text="PDF App Restrictor",
            theme_text_color="Primary",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        
        # Description
        description = MDLabel(
            text="Restrict app access until you read PDF pages for 5 minutes",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        
        # Buttons
        select_apps_btn = MDRaisedButton(
            text="Select Apps to Restrict",
            size_hint=(0.8, None),
            height=dp(50),
            pos_hint={"center_x": 0.5},
            on_release=lambda x: self.switch_screen("app_selection")
        )
        
        start_reading_btn = MDRaisedButton(
            text="Start Reading Session",
            size_hint=(0.8, None),
            height=dp(50),
            pos_hint={"center_x": 0.5},
            on_release=lambda x: self.switch_screen("pdf_reading")
        )
        
        # Status info
        status_info = self.service.get_status_info()
        status_text = f"Restricted Apps: {status_info['restricted_apps_count']}"
        if status_info['access_granted']:
            if 'time_remaining' in status_info:
                status_text += f"\nAccess granted for: {status_info['time_remaining']}"
            else:
                status_text += "\nAccess granted"
        else:
            status_text += "\nAccess blocked - Read PDF to unlock"

        status_label = MDLabel(
            text=status_text,
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        
        layout.add_widget(title)
        layout.add_widget(description)
        layout.add_widget(select_apps_btn)
        layout.add_widget(start_reading_btn)
        layout.add_widget(status_label)
        
        screen.add_widget(layout)
        self.screen_manager.add_widget(screen)

    def switch_screen(self, screen_name):
        """Switch to specified screen"""
        self.screen_manager.current = screen_name

    def get_installed_apps(self):
        """Get list of installed apps on Android device"""
        apps = []

        if platform == 'android':
            try:
                context = PythonActivity.mActivity
                pm = context.getPackageManager()
                packages = pm.getInstalledApplications(PackageManager.GET_META_DATA)

                for package in packages:
                    # Skip system apps and our own app
                    if (package.flags & ApplicationInfo.FLAG_SYSTEM) == 0:
                        app_name = str(pm.getApplicationLabel(package))
                        package_name = str(package.packageName)

                        # Skip our own app
                        if package_name != "org.example.pdfrestrictor":
                            apps.append({
                                'name': app_name,
                                'package': package_name
                            })

                # Sort apps by name
                apps.sort(key=lambda x: x['name'].lower())

            except Exception as e:
                print(f"Error getting installed apps: {e}")
                # Fallback with some common apps for testing
                apps = [
                    {'name': 'Chrome', 'package': 'com.android.chrome'},
                    {'name': 'YouTube', 'package': 'com.google.android.youtube'},
                    {'name': 'Instagram', 'package': 'com.instagram.android'},
                    {'name': 'WhatsApp', 'package': 'com.whatsapp'},
                    {'name': 'Facebook', 'package': 'com.facebook.katana'},
                ]
        else:
            # For testing on desktop
            apps = [
                {'name': 'Chrome', 'package': 'com.android.chrome'},
                {'name': 'YouTube', 'package': 'com.google.android.youtube'},
                {'name': 'Instagram', 'package': 'com.instagram.android'},
                {'name': 'WhatsApp', 'package': 'com.whatsapp'},
                {'name': 'Facebook', 'package': 'com.facebook.katana'},
            ]

        return apps

    def toggle_app_restriction(self, package_name):
        """Toggle restriction for an app"""
        if package_name in self.service.restricted_apps:
            self.service.restricted_apps.remove(package_name)
        else:
            self.service.restricted_apps.append(package_name)

    def save_app_selection(self, *args):
        """Save the selected apps and return to main screen"""
        self.service.save_data()
        self.switch_screen("main")

        # Update main screen status
        self.update_main_screen_status()

    def create_app_selection_screen(self):
        """Create screen for selecting apps to restrict"""
        screen = MDScreen(name="app_selection")

        layout = MDBoxLayout(orientation="vertical")

        # Toolbar
        toolbar = MDTopAppBar(
            title="Select Apps to Restrict",
            left_action_items=[["arrow-left", lambda x: self.switch_screen("main")]],
            elevation=2
        )

        # Scroll view for app list
        scroll = MDScrollView()
        app_list = MDList()

        # Get installed apps
        installed_apps = self.get_installed_apps()

        for app_info in installed_apps:
            app_name = app_info['name']
            package_name = app_info['package']

            # Create list item with checkbox
            item = OneLineAvatarIconListItem(
                text=app_name,
                on_release=lambda x, pkg=package_name: self.toggle_app_restriction(pkg)
            )

            # Add checkbox
            checkbox = MDCheckbox(
                size_hint=(None, None),
                size=(dp(48), dp(48)),
                pos_hint={'center_x': 0.5, 'center_y': 0.5},
                active=package_name in self.service.restricted_apps,
                on_active=lambda checkbox, value, pkg=package_name: self.toggle_app_restriction(pkg)
            )

            item.add_widget(checkbox)
            app_list.add_widget(item)

        scroll.add_widget(app_list)

        # Save button
        save_btn = MDRaisedButton(
            text="Save Selection",
            size_hint=(0.8, None),
            height=dp(50),
            pos_hint={"center_x": 0.5},
            on_release=self.save_app_selection
        )

        layout.add_widget(toolbar)
        layout.add_widget(scroll)
        layout.add_widget(save_btn)

        screen.add_widget(layout)
        self.screen_manager.add_widget(screen)

    def create_pdf_reading_screen(self):
        """Create screen for PDF reading with timer"""
        screen = MDScreen(name="pdf_reading")

        layout = MDBoxLayout(orientation="vertical")

        # Toolbar
        toolbar = MDTopAppBar(
            title="Read PDF Page",
            left_action_items=[["arrow-left", lambda x: self.switch_screen("main")]],
            elevation=2
        )

        # Timer display
        self.timer_label = MDLabel(
            text="Time remaining: 5:00",
            theme_text_color="Primary",
            font_style="H5",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )

        # Progress bar
        self.progress_bar = MDProgressBar(
            value=0,
            size_hint_y=None,
            height=dp(10)
        )

        # PDF page display area
        self.pdf_display = MDCard(
            size_hint=(0.95, 0.6),
            pos_hint={"center_x": 0.5},
            elevation=3,
            padding=dp(10)
        )

        # Page info
        self.page_info = MDLabel(
            text=f"Page {self.current_page + 1} of {len(self.pdf_pages) if self.pdf_pages else 0}",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(30)
        )

        # Navigation buttons
        nav_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(20),
            size_hint_y=None,
            height=dp(60),
            adaptive_width=True,
            pos_hint={"center_x": 0.5}
        )

        prev_btn = MDIconButton(
            icon="chevron-left",
            on_release=self.previous_page
        )

        next_btn = MDIconButton(
            icon="chevron-right",
            on_release=self.next_page
        )

        nav_layout.add_widget(prev_btn)
        nav_layout.add_widget(next_btn)

        # Start/Stop timer button
        self.timer_btn = MDRaisedButton(
            text="Start Reading Timer",
            size_hint=(0.8, None),
            height=dp(50),
            pos_hint={"center_x": 0.5},
            on_release=self.toggle_timer
        )

        layout.add_widget(toolbar)
        layout.add_widget(self.timer_label)
        layout.add_widget(self.progress_bar)
        layout.add_widget(self.pdf_display)
        layout.add_widget(self.page_info)
        layout.add_widget(nav_layout)
        layout.add_widget(self.timer_btn)

        screen.add_widget(layout)
        self.screen_manager.add_widget(screen)

    def previous_page(self, *args):
        """Navigate to previous PDF page"""
        if self.current_page > 0:
            self.current_page -= 1
            self.update_page_display()

    def next_page(self, *args):
        """Navigate to next PDF page"""
        if self.current_page < len(self.pdf_pages) - 1:
            self.current_page += 1
            self.update_page_display()

    def update_page_display(self):
        """Update the PDF page display"""
        if hasattr(self, 'page_info'):
            self.page_info.text = f"Page {self.current_page + 1} of {len(self.pdf_pages) if self.pdf_pages else 0}"

    def toggle_timer(self, *args):
        """Start or stop the reading timer"""
        if not self.timer_active:
            self.start_timer()
        else:
            self.stop_timer()

    def start_timer(self):
        """Start the 5-minute reading timer"""
        self.timer_active = True
        self.reading_timer = 300  # 5 minutes in seconds
        self.timer_btn.text = "Stop Reading Timer"

        # Schedule timer updates
        Clock.schedule_interval(self.update_timer, 1)

    def stop_timer(self):
        """Stop the reading timer"""
        self.timer_active = False
        self.timer_btn.text = "Start Reading Timer"
        Clock.unschedule(self.update_timer)

    def update_timer(self, dt):
        """Update timer display and progress"""
        if self.reading_timer > 0:
            self.reading_timer -= 1

            # Update timer display
            minutes = self.reading_timer // 60
            seconds = self.reading_timer % 60
            self.timer_label.text = f"Time remaining: {minutes}:{seconds:02d}"

            # Update progress bar
            progress = (300 - self.reading_timer) / 300 * 100
            self.progress_bar.value = progress

        else:
            # Timer finished
            self.timer_finished()

    def timer_finished(self):
        """Handle timer completion"""
        self.stop_timer()
        self.timer_label.text = "Reading session complete!"
        self.progress_bar.value = 100

        # Show completion dialog
        dialog = MDDialog(
            title="Reading Complete!",
            text="You have successfully read for 5 minutes. You can now access your restricted apps.",
            buttons=[
                MDRaisedButton(
                    text="OK",
                    on_release=lambda x: (dialog.dismiss(), self.switch_screen("main"))
                )
            ]
        )
        dialog.open()

        # Grant temporary access to restricted apps
        self.grant_temporary_access()

    def grant_temporary_access(self):
        """Grant temporary access to restricted apps"""
        # Use the service to grant access
        self.service.grant_access_after_reading()
        print("Temporary access granted to restricted apps for 1 hour")

    def update_main_screen_status(self):
        """Update the status display on main screen"""
        # This would update the main screen with current status
        pass


if __name__ == "__main__":
    PDFReadingAppRestrictor().run()
