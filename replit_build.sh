#!/bin/bash
# Replit Build Script for PDF App Restrictor
# Run this in a Replit Python environment

echo "Setting up build environment..."

# Install system dependencies
sudo apt-get update
sudo apt-get install -y openjdk-8-jdk build-essential libssl-dev libffi-dev python3-dev

# Set JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

# Install buildozer
pip install buildozer cython

# Build APK
echo "Building APK..."
buildozer android debug

echo "Build complete! Check the bin/ directory for your APK file."
