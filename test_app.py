#!/usr/bin/env python3
"""
Test script for PDF App Restrictor
Tests basic functionality without Android-specific features
"""

import os
import sys
import traceback

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import kivymd
        print("✓ KivyMD imported successfully")
    except ImportError as e:
        print(f"✗ KivyMD import failed: {e}")
        return False
    
    try:
        import kivy
        print("✓ Kivy imported successfully")
    except ImportError as e:
        print(f"✗ Kivy import failed: {e}")
        return False
    
    try:
        import fitz  # PyMuPDF
        print("✓ PyMuPDF imported successfully")
    except ImportError as e:
        print(f"✗ PyMuPDF import failed: {e}")
        return False
    
    try:
        from app_restrictor_service import app_restrictor_service
        print("✓ App restrictor service imported successfully")
    except ImportError as e:
        print(f"✗ App restrictor service import failed: {e}")
        return False
    
    return True

def test_pdf_loading():
    """Test PDF loading functionality"""
    print("\nTesting PDF loading...")
    
    if not os.path.exists("sample.pdf"):
        print("✗ sample.pdf not found")
        return False
    
    try:
        import fitz
        doc = fitz.open("sample.pdf")
        page_count = len(doc)
        doc.close()
        print(f"✓ PDF loaded successfully with {page_count} pages")
        return True
    except Exception as e:
        print(f"✗ PDF loading failed: {e}")
        return False

def test_service():
    """Test app restrictor service"""
    print("\nTesting app restrictor service...")
    
    try:
        from app_restrictor_service import app_restrictor_service
        
        # Test basic functionality
        service = app_restrictor_service
        
        # Test app list management
        test_apps = ["com.test.app1", "com.test.app2"]
        service.update_restricted_apps(test_apps)
        
        if service.restricted_apps == test_apps:
            print("✓ App restriction list management works")
        else:
            print("✗ App restriction list management failed")
            return False
        
        # Test access granting
        service.grant_access_after_reading()
        if service.is_access_granted():
            print("✓ Access granting works")
        else:
            print("✗ Access granting failed")
            return False
        
        # Test status info
        status = service.get_status_info()
        if isinstance(status, dict) and 'restricted_apps_count' in status:
            print("✓ Status info generation works")
        else:
            print("✗ Status info generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Service test failed: {e}")
        traceback.print_exc()
        return False

def test_main_app():
    """Test main app initialization"""
    print("\nTesting main app initialization...")
    
    try:
        # Set environment variable to prevent window creation
        os.environ['KIVY_NO_CONSOLELOG'] = '1'
        
        from main import PDFReadingAppRestrictor
        
        # Create app instance (but don't run it)
        app = PDFReadingAppRestrictor()
        
        # Test basic properties
        if hasattr(app, 'service') and hasattr(app, 'pdf_pages'):
            print("✓ Main app initialization works")
            return True
        else:
            print("✗ Main app initialization failed - missing attributes")
            return False
            
    except Exception as e:
        print(f"✗ Main app test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("PDF App Restrictor - Test Suite")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_pdf_loading,
        test_service,
        test_main_app
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The app should work correctly.")
        return True
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
