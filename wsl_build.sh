#!/bin/bash
# WSL Build Script for PDF App Restrictor
# Run this in WSL (Windows Subsystem for Linux)

echo "PDF App Restrictor - WSL Build Script"
echo "====================================="

# Update system
sudo apt update

# Install dependencies
echo "Installing dependencies..."
sudo apt install -y python3-pip python3-dev openjdk-8-jdk build-essential libssl-dev libffi-dev

# Set JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
echo "export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64" >> ~/.bashrc

# Install buildozer
echo "Installing buildozer..."
pip3 install buildozer cython

# Build APK
echo "Building APK..."
buildozer android debug

echo "Build complete!"
echo "Your APK should be in the bin/ directory"
ls -la bin/
